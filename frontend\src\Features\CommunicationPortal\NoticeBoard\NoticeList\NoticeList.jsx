import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { FaPlus, FaImage, FaFilePdf, FaFileWord, FaCalendarAlt } from "react-icons/fa";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BiFilter } from "react-icons/bi";

import usePinPost from "../components/PinPost";
import Calendar from "../../Announcements/components/Calendar";
import ConfirmationMessageBox from "../../../../Components/MessageBox/ConfirmationMessageBox";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import ImageSlider from "../../../../Components/Modal/ImageSlider";
import DocumentViewer from "../../../../Components/FileViewer/DocumentViewer";
import { useNotices } from "../../../../hooks/useNotices";
import NoticeListPreview from "./NoticeListPreview";
import FilterSelectModal from "../../../../Components/FilterSelect/FilterSelectModal";
import { clearUserCountCache } from "../hooks/useUserCount";

const NoticeList = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Redux hooks for notices
  const {
    notices: reduxNotices,
    loading: reduxLoading,
    deleteSuccess,
    message,
    loadNotices: loadNoticesRedux,
    updateAllStatuses: updateAllStatusesRedux,
    removeNotice: removeNoticeRedux,
    moveNoticeToExpired: moveNoticeToExpiredRedux,
    restoreExpiredNotice: restoreExpiredNoticeRedux,

    clearAllSuccess: clearAllSuccessRedux
  } = useNotices();

  const [activeTab, setActiveTab] = useState(() => {
    return (
      parseInt(localStorage.getItem("noticeActiveTab")) ||
      location.state?.activeTab ||
      1
    );
  });
  const [myPostChecked, setMyPostChecked] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState([]);
  const [selectedLabel, setSelectedLabel] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [availableLabels, setAvailableLabels] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isImageSliderOpen, setIsImageSliderOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [isDocumentViewerOpen, setIsDocumentViewerOpen] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const dropdownRef = useRef(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [noticeToDelete, setNoticeToDelete] = useState(null);
  const [showExpireConfirmation, setShowExpireConfirmation] = useState(false);
  const [noticeToExpire, setNoticeToExpire] = useState(null);
  const [showRestoreConfirmation, setShowRestoreConfirmation] = useState(false);
  const [noticeToRestore, setNoticeToRestore] = useState(null);

  const [selectedDate, setSelectedDate] = useState("");
  const [filteredNotices, setFilteredNotices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  const { pinPost, unpinPost } = usePinPost();
  const searchInputRef = useRef(null);

  // Load notices on component mount and when activeTab changes
  useEffect(() => {
    loadNotices();
  }, [activeTab]);

  // Handle success messages from Redux
  useEffect(() => {
    if (deleteSuccess && message) {
      setSuccessMessage(message);
      setShowSuccessMessage(true);
      clearAllSuccessRedux();
    }
  }, [deleteSuccess, message, clearAllSuccessRedux]);

  // Save active tab to localStorage
  useEffect(() => {
    localStorage.setItem("noticeActiveTab", activeTab.toString());
  }, [activeTab]);

  // Clear user count cache when component unmounts
  useEffect(() => {
    return () => {
      clearUserCountCache();
    };
  }, []);

  // Handle clicks outside dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const loadNotices = async () => {
    setIsLoading(true);
    try {
      await loadNoticesRedux();
    } catch (error) {
      console.error("Error loading notices:", error);
      setErrorMessage("Failed to load notices. Please try again.");
      setShowErrorMessage(true);
    } finally {
      setIsLoading(false);
    }
  };

  const updateAllStatuses = async () => {
    try {
      await updateAllStatusesRedux();
      await loadNotices(); // Reload notices after status update
    } catch (error) {
      console.error("Error updating statuses:", error);
      setErrorMessage("Failed to update notice statuses. Please try again.");
      setShowErrorMessage(true);
    }
  };

  // Filter notices based on active tab, search term, priority, label, and date
  useEffect(() => {
    let filtered = reduxNotices || [];

    // Filter by status based on active tab
    if (activeTab === 1) {
      filtered = filtered.filter((notice) => notice.status === "ongoing");
    } else if (activeTab === 2) {
      filtered = filtered.filter((notice) => notice.status === "upcoming");
    } else if (activeTab === 3) {
      filtered = filtered.filter((notice) => notice.status === "expired");
    }

    // Filter by search term (search in internal title and author)
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (notice) =>
          (notice.internalTitle && notice.internalTitle.toLowerCase().includes(searchLower)) ||
          (notice.author && notice.author.toLowerCase().includes(searchLower))
      );
    }

    // Filter by priority
    if (selectedPriority.length > 0) {
      filtered = filtered.filter((notice) =>
        selectedPriority.includes(notice.priority)
      );
    }

    // Filter by label
    if (selectedLabel.length > 0) {
      filtered = filtered.filter((notice) =>
        selectedLabel.includes(notice.label)
      );
    }

    // Filter by date
    if (selectedDate) {
      filtered = filtered.filter((notice) => {
        if (!notice.startDate || !notice.endDate) return false;
        const selectedDateObj = new Date(selectedDate);
        selectedDateObj.setHours(0, 0, 0, 0);
        const noticeStartDate = new Date(notice.startDate);
        noticeStartDate.setHours(0, 0, 0, 0);
        const noticeEndDate = new Date(notice.endDate);
        noticeEndDate.setHours(0, 0, 0, 0);
        return (
          selectedDateObj >= noticeStartDate &&
          selectedDateObj <= noticeEndDate
        );
      });
    }

    // Filter by "My Posts" if checked
    if (myPostChecked) {
      // This would need to be implemented based on current user
      // For now, we'll skip this filter
    }

    setFilteredNotices(filtered);
  }, [
    reduxNotices,
    activeTab,
    searchTerm,
    selectedPriority,
    selectedLabel,
    selectedDate,
    myPostChecked,
  ]);

  // Extract unique labels from notices for filter options
  useEffect(() => {
    const labels = [...new Set(reduxNotices?.map((notice) => notice.label).filter(Boolean))];
    setAvailableLabels(labels);
  }, [reduxNotices]);

  const handleTabChange = (tabNumber) => {
    setActiveTab(tabNumber);
  };

  const handleAddNotice = () => {
    navigate("/communication-portal/notice-board/add");
  };

  const handleEditNotice = (noticeId) => {
    navigate(`/communication-portal/notice-board/edit/${noticeId}`);
  };

  const handleDeleteNotice = (notice) => {
    setNoticeToDelete(notice);
    setShowDeleteConfirmation(true);
  };

  const confirmDeleteNotice = async () => {
    if (noticeToDelete) {
      try {
        await removeNoticeRedux(noticeToDelete.id);
        setShowDeleteConfirmation(false);
        setNoticeToDelete(null);
      } catch (error) {
        console.error("Error deleting notice:", error);
        setErrorMessage("Failed to delete notice. Please try again.");
        setShowErrorMessage(true);
      }
    }
  };

  const handleExpireNotice = (notice) => {
    setNoticeToExpire(notice);
    setShowExpireConfirmation(true);
  };

  const confirmExpireNotice = async () => {
    if (noticeToExpire) {
      try {
        await moveNoticeToExpiredRedux(noticeToExpire.id);
        setShowExpireConfirmation(false);
        setNoticeToExpire(null);
        setSuccessMessage("Notice has been moved to expired.");
        setShowSuccessMessage(true);
      } catch (error) {
        console.error("Error expiring notice:", error);
        setErrorMessage("Failed to expire notice. Please try again.");
        setShowErrorMessage(true);
      }
    }
  };

  const handleRestoreNotice = (notice) => {
    setNoticeToRestore(notice);
    setShowRestoreConfirmation(true);
  };

  const confirmRestoreNotice = async () => {
    if (noticeToRestore) {
      try {
        await restoreExpiredNoticeRedux(noticeToRestore.id);
        setShowRestoreConfirmation(false);
        setNoticeToRestore(null);
        setSuccessMessage("Notice has been restored.");
        setShowSuccessMessage(true);
      } catch (error) {
        console.error("Error restoring notice:", error);
        setErrorMessage("Failed to restore notice. Please try again.");
        setShowErrorMessage(true);
      }
    }
  };

  const handlePinToggle = async (notice) => {
    try {
      if (notice.isPinned) {
        await unpinPost(notice.id);
      } else {
        await pinPost(notice.id);
      }
      await loadNotices(); // Reload to get updated pin status
    } catch (error) {
      console.error("Error toggling pin:", error);
      setErrorMessage("Failed to update pin status. Please try again.");
      setShowErrorMessage(true);
    }
  };

  const handleDropdownToggle = (noticeId) => {
    setOpenDropdownId(openDropdownId === noticeId ? null : noticeId);
  };

  const handleFilterToggle = () => {
    setIsFilterExpanded(!isFilterExpanded);
  };

  const handleImageClick = (attachment, notice) => {
    const allAttachments = notice.attachments || [];
    const imageAttachments = allAttachments.filter(
      (att) =>
        isImage(att.file_name || att.name) ||
        (!att.file_name && !att.name && !isDocument(att.file_name || att.name))
    );
    const clickedIndex = imageAttachments.findIndex(
      (img) =>
        (img.file_url || img.url || img) ===
        (attachment.file_url || attachment.url || attachment)
    );
    const formattedImages = imageAttachments.map((img, index) => ({
      src: img.file_url || img.url || img,
      alt: img.file_name || img.name || `Image ${index + 1}`,
      name: img.file_name || img.name || `image-${index + 1}`
    }));
    setSelectedImages(formattedImages);
    setSelectedImageIndex(clickedIndex >= 0 ? clickedIndex : 0);
    setIsImageSliderOpen(true);
  };

  const handleDocumentClick = (attachment) => {
    setSelectedDocument(attachment);
    setIsDocumentViewerOpen(true);
  };

  const isImage = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    return ["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(extension);
  };

  const isDocument = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    return ["pdf", "doc", "docx"].includes(extension);
  };

  const getFileIcon = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
        return <FaFilePdf className="w-6 h-6 text-black font-bold" />;
      case "doc":
      case "docx":
        return <FaFileWord className="w-6 h-6 text-blue-500" />;
      default:
        return <FaImage className="w-6 h-6 text-gray-400" />;
    }
  };





  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-900">
              Notice Board
            </h1>

            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={myPostChecked}
                  onChange={(e) => setMyPostChecked(e.target.checked)}
                  className="form-checkbox h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary"
                />
                <span className="ml-2 text-sm text-primary">My Post</span>
              </label>

              <button
                onClick={handleFilterToggle}
                className={`flex items-center justify-center px-4 py-2 rounded-lg transition-colors text-sm font-medium w-[99px] h-[48px] ${
                  isFilterExpanded
                    ? "bg-primary text-white"
                    : "bg-white text-primary border border-primary hover:bg-gray-50"
                }`}
              >
                <BiFilter className="mr-2 w-4 h-4" />
                Filter
              </button>

              <button
                onClick={handleAddNotice}
                className="flex items-center justify-center bg-primary text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium w-[150px] h-[48px]"
              >
                <FaPlus className="mr-2 w-4 h-4" />
                Add Notice
              </button>
            </div>
          </div>



          {isFilterExpanded && (
            <div className="flex items-center justify-end space-x-6">
              <div className="min-w-[160px]">
                <Calendar
                  value={selectedDate}
                  onChange={setSelectedDate}
                  placeholder="Select Date"
                />
              </div>

              <div className="min-w-[160px]">
                <FilterSelectModal
                  placeholder="Select Priority"
                  options={[
                    { value: "Urgent", label: "Urgent" },
                    { value: "High", label: "High" },
                    { value: "Normal", label: "Normal" },
                    { value: "Low", label: "Low" }
                  ]}
                  value={selectedPriority}
                  onApply={setSelectedPriority}
                  className="w-full"
                />
              </div>

              <div className="min-w-[160px]">
                <FilterSelectModal
                  placeholder="Select Label"
                  options={availableLabels.map((label) => ({
                    value: label,
                    label: label
                  }))}
                  value={selectedLabel}
                  onApply={setSelectedLabel}
                  className="w-full"
                />
              </div>

              <div className="relative min-w-[200px] max-w-[250px]">
                <BiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary w-4 h-4" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search list..."
                  className="w-full h-[42px] pl-10 pr-4 border border-primary rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm text-primary placeholder-primary"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="rounded-[27px] bg-white">
        <div className="p-4">
          <div className="flex mb-4 bg-[#3C9D9B1A] rounded">
            <button
              className={`flex-1 w-full py-2 font-[600] text-[#090909] ${
                activeTab === 1
                  ? "border border-primary"
                  : "border border-transparent"
              }`}
              onClick={() => handleTabChange(1)}
            >
              Ongoing
            </button>
            <button
              className={`flex-1 w-full py-2 font-[600] text-[#090909] ${
                activeTab === 2
                  ? "border border-primary"
                  : "border border-transparent"
              }`}
              onClick={() => handleTabChange(2)}
            >
              Upcoming
            </button>
            <button
              className={`flex-1 w-full py-2 font-[600] text-[#090909] ${
                activeTab === 3
                  ? "border border-primary"
                  : "border border-transparent"
              }`}
              onClick={() => handleTabChange(3)}
            >
              Expired
            </button>
          </div>

      {/* Notice List */}
          {filteredNotices.length === 0 ? (
            <div className="flex flex-col justify-center items-center min-h-[300px] text-center w-full">
              <h3 className="text-xl font-semibold text-gray-900">
                No notices found
              </h3>
            </div>
          ) : (
            <NoticeListPreview
              notices={filteredNotices}
              loading={isLoading || reduxLoading}
              openDropdownId={openDropdownId}
              dropdownRef={dropdownRef}
              currentTab={activeTab}
              handleDropdownToggle={handleDropdownToggle}
              onEdit={handleEditNotice}
              onDelete={handleDeleteNotice}
              onExpire={handleExpireNotice}
              onRestore={handleRestoreNotice}
              onPinToggle={handlePinToggle}
              onImageClick={handleImageClick}
              activeTab={activeTab}
              handleDocumentClick={handleDocumentClick}
              isDocument={isDocument}
              getFileIcon={getFileIcon}
            />
          )}
        </div>
      </div>

      {/* Modals and Dialogs */}
      {showDeleteConfirmation && (
        <ConfirmationMessageBox
          message={`Are you sure you want to delete this notice?`}
          onConfirm={confirmDeleteNotice}
          onCancel={() => {
            setShowDeleteConfirmation(false);
            setNoticeToDelete(null);
          }}
        />
      )}

      {showExpireConfirmation && (
        <ConfirmationMessageBox
          message={`Are you sure you want to move this notice to expired?`}
          onConfirm={confirmExpireNotice}
          onCancel={() => {
            setShowExpireConfirmation(false);
            setNoticeToExpire(null);
          }}
        />
      )}

      {showRestoreConfirmation && (
        <ConfirmationMessageBox
          message={`Are you sure you want to restore this notice?`}
          onConfirm={confirmRestoreNotice}
          onCancel={() => {
            setShowRestoreConfirmation(false);
            setNoticeToRestore(null);
          }}
        />
      )}

      {showSuccessMessage && (
        <MessageBox
          message={successMessage}
          clearMessage={() => setShowSuccessMessage(false)}
        />
      )}

      {showErrorMessage && (
        <MessageBox
          message={errorMessage}
          clearMessage={() => setShowErrorMessage(false)}
        />
      )}

      <ImageSlider
        isOpen={isImageSliderOpen}
        onClose={() => setIsImageSliderOpen(false)}
        images={selectedImages}
        initialIndex={selectedImageIndex}
      />



      {isDocumentViewerOpen && selectedDocument && (
        <DocumentViewer
          fileUrl={
            selectedDocument.file_url ||
            selectedDocument.url ||
            selectedDocument.base64
          }
          fileName={selectedDocument.file_name || selectedDocument.name}
          onClose={() => setIsDocumentViewerOpen(false)}
        />
      )}

    </div>
  );
};

export default NoticeList;
