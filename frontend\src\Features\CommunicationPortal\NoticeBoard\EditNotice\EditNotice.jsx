import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>pinner } from "react-icons/fa";
import EditNoticeForm from "./EditNoticeForm";
import NoticePreview from "../components/NoticePreview";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import { useNotices } from "../../../../hooks/useNotices";
import useCurrentUser from "../hooks/useCurrentUser";


const EditNotice = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [initialLoading, setInitialLoading] = useState(true);

  const {
    selectedNotice,
    loading,
    updating,
    updateSuccess,
    updateError,
    message,
    loadNotice: loadNoticeRedux,
    updateNotice: updateNoticeRedux,
    clearAllSuccess: clearAllSuccessRedux,
    clearErrors: clearErrorsRedux
  } = useNotices();

  const currentUser = useCurrentUser();

  // Load notice data on mount
  useEffect(() => {
    const loadNoticeData = async () => {
      if (id) {
        try {
          setInitialLoading(true);
          await loadNoticeRedux(id);
        } catch (error) {
          console.error("Error loading notice:", error);
          setErrorMessage("Failed to load notice data. Please try again.");
          setShowErrorMessage(true);
        } finally {
          setInitialLoading(false);
        }
      }
    };

    loadNoticeData();
  }, [id, loadNoticeRedux]);

  // Handle success
  useEffect(() => {
    if (updateSuccess && message) {
      setShowSuccessMessage(true);
      // Navigate back to notice list after a short delay
      setTimeout(() => {
        navigate("/communication-portal/notice-board", {
          state: { activeTab: 1 } // Go to ongoing tab
        });
      }, 2000);
    }
  }, [updateSuccess, message, navigate]);

  // Handle errors
  useEffect(() => {
    if (updateError) {
      setErrorMessage(
        typeof updateError === 'string' 
          ? updateError 
          : updateError.message || "Failed to update notice. Please try again."
      );
      setShowErrorMessage(true);
    }
  }, [updateError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAllSuccessRedux();
      clearErrorsRedux();
    };
  }, [clearAllSuccessRedux, clearErrorsRedux]);

  const handleBack = () => {
    navigate("/communication-portal/notice-board");
  };

  const handlePreview = (formData) => {
    setPreviewData(formData);
    setShowPreview(true);
  };

  const handleSubmit = async (formData) => {
    try {
      await updateNoticeRedux({ id: parseInt(id), data: formData });
    } catch (error) {
      console.error("Error updating notice:", error);
      setErrorMessage("Failed to update notice. Please try again.");
      setShowErrorMessage(true);
    }
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    setPreviewData(null);
  };

  if (initialLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center gap-3 text-gray-600">
          <FaSpinner className="w-6 h-6 animate-spin" />
          <span>Loading notice data...</span>
        </div>
      </div>
    );
  }

  if (!selectedNotice) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Notice Not Found</h2>
          <p className="text-gray-600 mb-4">The notice you're looking for doesn't exist or has been deleted.</p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Back to Notice List
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              disabled={updating}
            >
              <FaArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Edit Notice</h1>
              <p className="text-gray-600 mt-1">
                {selectedNotice.internalTitle || `Notice #${selectedNotice.id}`}
              </p>
            </div>
          </div>
          
          {previewData && (
            <button
              onClick={() => setShowPreview(true)}
              className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
              disabled={updating}
            >
              <FaEye className="w-4 h-4" />
              Preview
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <EditNoticeForm
                notice={selectedNotice}
                onSubmit={handleSubmit}
                onPreview={handlePreview}
                loading={updating}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && previewData && (
        <NoticePreview
          data={previewData}
          onClose={handleClosePreview}
          onSubmit={handleSubmit}
          loading={updating}
          isEdit={true}
          currentUser={currentUser}
        />
      )}

      {/* Success Message */}
      {showSuccessMessage && (
        <MessageBox
          message={message || "Notice has been successfully updated!"}
          clearMessage={() => setShowSuccessMessage(false)}
        />
      )}

      {/* Error Message */}
      {showErrorMessage && (
        <MessageBox
          error={errorMessage}
          clearMessage={() => {
            setShowErrorMessage(false);
            clearErrorsRedux();
          }}
        />
      )}
    </div>
  );
};

export default EditNotice;
