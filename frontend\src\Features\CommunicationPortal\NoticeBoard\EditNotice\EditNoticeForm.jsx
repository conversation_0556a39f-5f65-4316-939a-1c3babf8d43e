import React, { useState, useEffect } from "react";
import { Controller } from "react-hook-form";
import { <PERSON>a<PERSON><PERSON>, FaSave, FaSpinner, FaUpload, FaTimes, FaImage } from "react-icons/fa";
import { BsExclamationTriangle } from "react-icons/bs";
import TextInputComponent from "../../../../Components/FormComponent/TextInputComponent";
import TextareaComponent from "../../../../Components/FormComponent/TextareaComponent";
import LabelSelector from "../components/LabelSelector";
import PriorityDropdown from "../components/PriorityDropdown";
import PostAsSelector from "../components/PostAsSelector";
import TowerUnitSelector from "../components/TowerUnitSelector";
import UserCountDisplay from "../components/UserCountDisplay";

/**
 * EditNoticeForm Component
 * Comprehensive form for editing notices with advanced validation and file handling
 */
const EditNoticeForm = ({
  // Form props
  control,
  handleSubmit,
  watch,
  setValue,
  errors,
  isSubmitting,
  onSubmit,

  // State props
  currentUser,
  attachments,
  notice,
  hasFormBeenModified,

  // Error states
  titleWordLimitError,
  fileUploadError,
  dateOrderError,

  // Handlers
  handleTitleChange,
  getTitleWordCount,
  handleFileUpload,
  removeAttachment,
  handleMemberSelect,
  handleGroupSelect,
  isFormValid,

  // Watched values
  watchedValues,
  selectedTowers
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);

  // Handle drag and drop for file upload
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      // Create a mock event object for handleFileUpload
      const mockEvent = {
        target: { files: files, value: '' }
      };
      handleFileUpload(mockEvent);
    }
  };

  // Handle image preview
  const handleImagePreview = (attachment) => {
    setPreviewImage(attachment);
    setShowPreviewModal(true);
  };

  // Close preview modal
  const closePreviewModal = () => {
    setShowPreviewModal(false);
    setPreviewImage(null);
  };

  // Format file size for display
  const formatFileSize = (size) => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-xl font-semibold text-gray-900">Edit Notice Details</h2>
        <p className="text-sm text-gray-500 mt-1">
          Update the notice information and preview changes in real-time
        </p>
        {hasFormBeenModified && (
          <div className="mt-2 flex items-center text-amber-600 text-sm">
            <BsExclamationTriangle className="w-4 h-4 mr-1" />
            You have unsaved changes
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Title <span className="text-red-500">*</span>
          </label>
          <Controller
            name="title"
            control={control}
            render={({ field: { onChange, value, ...field } }) => (
              <div>
                <TextInputComponent
                  {...field}
                  label=""
                  value={value || ''}
                  onChange={(e) => handleTitleChange(e.target.value, onChange)}
                  placeholder="Enter notice title (max 10 words)"
                  error={errors.title?.message || titleWordLimitError}
                />
                <div className="flex justify-between items-center mt-1">
                  <p className="text-xs text-gray-500">
                    {getTitleWordCount(value || 0)}/10 words
                  </p>
                  {titleWordLimitError && (
                    <p className="text-xs text-red-600">{titleWordLimitError}</p>
                  )}
                </div>
              </div>
            )}
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
          )}
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <TextareaComponent
                {...field}
                label=""
                value={field.value || ''}
                placeholder="Enter notice description (optional, max 100 words)"
                rows={4}
                error={errors.description?.message}
              />
            )}
          />
          <div className="flex justify-between items-center mt-1">
            <p className="text-xs text-gray-500">
              {getTitleWordCount(watchedValues.description || 0)}/100 words
            </p>
          </div>
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
          )}
        </div>

        {/* Images Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Images <span className="text-red-500">*</span>
          </label>
          
          {/* Upload Area */}
          <div
            className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              isDragOver
                ? 'border-blue-400 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileUpload}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
            <FaUpload className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">
              <span className="font-medium">Click to upload</span> or drag and drop
            </p>
            <p className="text-xs text-gray-500">
              PNG, JPG, GIF up to 5MB each (max 10 images)
            </p>
          </div>

          {/* File Upload Error */}
          {fileUploadError && (
            <p className="mt-2 text-sm text-red-600">{fileUploadError}</p>
          )}

          {/* Attachments List */}
          {attachments.length > 0 && (
            <div className="mt-4 space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-700">
                  Uploaded Images ({attachments.length}/10)
                </h4>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {attachments.map((attachment, index) => (
                  <div key={attachment.id || index} className="relative group">
                    <div className="relative bg-gray-100 rounded-lg overflow-hidden aspect-square">
                      {/* Image Preview */}
                      <img
                        src={attachment.url || attachment.base64 || attachment.preview}
                        alt={attachment.name}
                        className="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
                        onClick={() => handleImagePreview(attachment)}
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                      
                      {/* Fallback icon */}
                      <div className="hidden w-full h-full items-center justify-center bg-gray-200">
                        <FaImage className="w-8 h-8 text-gray-400" />
                      </div>

                      {/* Remove Button */}
                      <button
                        type="button"
                        onClick={() => removeAttachment(attachment.id)}
                        className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <FaTimes className="w-3 h-3" />
                      </button>

                      {/* Existing File Indicator */}
                      {attachment.isExisting && (
                        <div className="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                          Existing
                        </div>
                      )}
                    </div>
                    
                    {/* File Info */}
                    <div className="mt-2">
                      <p className="text-xs text-gray-600 truncate" title={attachment.name}>
                        {attachment.name}
                      </p>
                      {attachment.file && (
                        <p className="text-xs text-gray-400">
                          {formatFileSize(attachment.file.size)}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Validation Error */}
          {errors.attachments && (
            <p className="mt-2 text-sm text-red-600">{errors.attachments.message}</p>
          )}
        </div>

        {/* Priority and Label */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority <span className="text-red-500">*</span>
            </label>
            <Controller
              name="priority"
              control={control}
              render={({ field }) => (
                <PriorityDropdown
                  value={field.value}
                  onChange={field.onChange}
                  error={errors.priority?.message}
                />
              )}
            />
            {errors.priority && (
              <p className="mt-1 text-sm text-red-600">{errors.priority.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Label <span className="text-red-500">*</span>
            </label>
            <Controller
              name="label"
              control={control}
              render={({ field }) => (
                <LabelSelector
                  value={field.value}
                  onChange={field.onChange}
                  error={errors.label?.message}
                />
              )}
            />
            {errors.label && (
              <p className="mt-1 text-sm text-red-600">{errors.label.message}</p>
            )}
          </div>
        </div>

        {/* Date and Time */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Date & Time <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-2 gap-2">
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                )}
              />
              <Controller
                name="startTime"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="time"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                )}
              />
            </div>
            {(errors.startDate || errors.startTime) && (
              <p className="mt-1 text-sm text-red-600">
                {errors.startDate?.message || errors.startTime?.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              End Date & Time <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-2 gap-2">
              <Controller
                name="endDate"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                )}
              />
              <Controller
                name="endTime"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="time"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                )}
              />
            </div>
            {(errors.endDate || errors.endTime || dateOrderError) && (
              <p className="mt-1 text-sm text-red-600">
                {errors.endDate?.message || errors.endTime?.message || dateOrderError}
              </p>
            )}
          </div>
        </div>

        {/* Post As */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Post As <span className="text-red-500">*</span>
          </label>
          <Controller
            name="postAs"
            control={control}
            render={({ field }) => (
              <PostAsSelector
                value={field.value}
                selectedGroup={watchedValues.selectedGroupId}
                selectedMember={watchedValues.selectedMemberId}
                onChange={(postAs, groupId, memberId) => {
                  field.onChange(postAs);
                  handleGroupSelect(groupId ? { id: groupId, name: watchedValues.selectedGroupName } : null);
                  handleMemberSelect(memberId ? { id: memberId, name: watchedValues.selectedMemberName } : null);
                }}
                currentUser={currentUser}
                error={errors.postAs?.message}
              />
            )}
          />
          {errors.postAs && (
            <p className="mt-1 text-sm text-red-600">{errors.postAs.message}</p>
          )}
        </div>

        {/* Tower and Unit Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Audience <span className="text-red-500">*</span>
          </label>
          <Controller
            name="selectedTowers"
            control={control}
            render={({ field }) => (
              <TowerUnitSelector
                selectedTowers={field.value || []}
                selectedUnits={watchedValues.selectedUnits || []}
                onChange={(towers, units) => {
                  field.onChange(towers);
                  setValue('selectedUnits', units);
                }}
                error={errors.selectedTowers?.message}
              />
            )}
          />
          {errors.selectedTowers && (
            <p className="mt-1 text-sm text-red-600">{errors.selectedTowers.message}</p>
          )}
        </div>

        {/* User Count Display */}
        {watchedValues.selectedUnits && watchedValues.selectedUnits.length > 0 && (
          <UserCountDisplay unitIds={watchedValues.selectedUnits} />
        )}

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            <span className="text-red-500">*</span> Required fields
          </div>
          
          <div className="flex gap-3">
            <button
              type="submit"
              disabled={!isFormValid() || isSubmitting || !!dateOrderError}
              className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <FaSpinner className="w-4 h-4 animate-spin" />
              ) : (
                <FaSave className="w-4 h-4" />
              )}
              {isSubmitting ? "Updating..." : "Update Notice"}
            </button>
          </div>
        </div>
      </form>

      {/* Image Preview Modal */}
      {showPreviewModal && previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={closePreviewModal}
              className="absolute top-4 right-4 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-colors z-10"
            >
              <FaTimes className="w-4 h-4" />
            </button>
            <img
              src={previewImage.url || previewImage.base64 || previewImage.preview}
              alt={previewImage.name}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded">
              {previewImage.name}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EditNoticeForm;
